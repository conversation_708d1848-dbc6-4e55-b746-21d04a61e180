import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';

class SignatureAddUserWidget extends StatefulWidget {
  const SignatureAddUserWidget({super.key});

  @override
  State<SignatureAddUserWidget> createState() => _SignatureAddUserWidgetState();
}

class _SignatureAddUserWidgetState extends State<SignatureAddUserWidget> {
  // 表单控制器
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _areaController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _roomController = TextEditingController();
  final TextEditingController _hallController = TextEditingController();
  final TextEditingController _kitchenController = TextEditingController();
  final TextEditingController _toiletController = TextEditingController();

  // 下拉选择值
  String? _projectStatusId;
  String? _firstSourceId;
  String? _firstSourceDetailId;
  String? _sourceId;
  String? _sourceDetailId;
  String? _houseType;
  String? _decorationTypeId;
  String? _decorationAreaId;

  // API 数据列表
  List<Map<String, dynamic>> firstSources = [];
  List<Map<String, dynamic>> firstSourceDetails = [];
  List<Map<String, dynamic>> sources = [];
  List<Map<String, dynamic>> sourceDetails = [];
  List<Map<String, dynamic>> decorationTypes = [];
  List<Map<String, dynamic>> roomTypes = [];
  List<Map<String, dynamic>> decorationAreas = [];
  List<Map<String, dynamic>> intentionTypes = [];

  // 加载状态
  bool isLoadingFirstSources = false;
  bool isLoadingFirstSourceDetails = false;
  bool isLoadingSources = false;
  bool isLoadingSourceDetails = false;
  bool isLoadingDecorationTypes = false;
  bool isLoadingRoomTypes = false;
  bool isLoadingDecorationAreas = false;
  bool isLoadingIntentionTypes = false;

  @override
  void initState() {
    super.initState();
    // 初始化户型控制器的默认值
    _roomController.text = '2';
    _hallController.text = '2';
    _kitchenController.text = '1';
    _toiletController.text = '1';

    // 加载 API 数据
    _loadFirstSources();
    _loadSources();
    _loadDecorationTypes();
    _loadRoomTypes();
    _loadDecorationAreas();
    _loadIntentionTypes();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _nameController.dispose();
    _areaController.dispose();
    _addressController.dispose();
    _roomController.dispose();
    _hallController.dispose();
    _kitchenController.dispose();
    _toiletController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('新增用户'),
        titleTextStyle: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18.sp,
          color: const Color(0xFF333333),
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: const Color.fromRGBO(0, 0, 0, 1),
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.8],
            colors: [
              Color(0xFFFFF4E0), // #FFF4E0 顶部颜色
              Colors.white, // 白色 底部颜色
            ],
          ),
        ),
        child: SafeArea(
          child: GestureDetector(
            onTap: () {
              // 点击空白处收起键盘
              FocusScope.of(context).unfocus();
            },
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(16.w),
                    child: _buildForm(),
                  ),
                ),
                _buildConfirmButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildForm() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFieldWithDivider(_buildPhoneField()),
          _buildFieldWithDivider(_buildNameField()),
          _buildFieldWithDivider(_buildProjectStatusField()),
          _buildFieldWithDivider(_buildFirstSourceField()),
          _buildFieldWithDivider(_buildFirstSourceDescriptionField()),
          _buildFieldWithDivider(_buildSourceField()),
          _buildFieldWithDivider(_buildSourceDescriptionField()),
          _buildFieldWithDivider(_buildHouseTypeField()),
          _buildFieldWithDivider(_buildRoomTypeField()),
          _buildFieldWithDivider(_buildAreaField()),
          _buildFieldWithDivider(_buildDecorationTypeField()),
          _buildFieldWithDivider(_buildDecorationAreaField(),
              isLast: _decorationAreaId == null),
          if (_decorationAreaId != null)
            _buildFieldWithDivider(_buildDetailAddressField(), isLast: true),
        ],
      ),
    );
  }

  Widget _buildFieldWithDivider(Widget field, {bool isLast = false}) {
    return Column(
      children: [
        Container(
          height: 52.h,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          alignment: Alignment.center,
          child: field,
        ),
        if (!isLast)
          Divider(
            height: 1.h,
            thickness: 1.h,
            color: Colors.grey[200],
            indent: 16.w,
            endIndent: 16.w,
          ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '手机号：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  textAlign: TextAlign.right,
                  decoration: InputDecoration(
                    hintText: '请输入',
                    hintStyle: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[400],
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(horizontal: 8.w),
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Container(
                height: 32.h,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Color(0xFFFFC276),
                      Color(0xFFFFAF2A),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  '校验重单',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '姓名：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: TextField(
            controller: _nameController,
            textAlign: TextAlign.right,
            decoration: InputDecoration(
              hintText: '请输入',
              hintStyle: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[400],
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8.w),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProjectStatusField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '项目状态：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showProjectStatusPicker(),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getProjectStatusDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _projectStatusId != null
                          ? const Color(0xFF333333)
                          : Colors.grey[400],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[400],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFirstSourceField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '首次来源：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showFirstSourcePicker(),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getFirstSourceDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _firstSourceId != null
                          ? const Color(0xFF333333)
                          : Colors.grey[400],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[400],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFirstSourceDescriptionField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '首次来源明细：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: _firstSourceId != null
                ? () => _showFirstSourceDescriptionPicker()
                : null,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getFirstSourceDetailDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _firstSourceDetailId != null
                          ? const Color(0xFF333333)
                          : _firstSourceId != null
                              ? Colors.grey[400]
                              : Colors.grey[300],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: _firstSourceId != null
                        ? Colors.grey[400]
                        : Colors.grey[300],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSourceField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '来源：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showSourcePicker(),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getSourceDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _sourceId != null
                          ? const Color(0xFF333333)
                          : Colors.grey[400],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[400],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSourceDescriptionField() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.red,
                ),
              ),
              TextSpan(
                text: '来源明细：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap:
                _sourceId != null ? () => _showSourceDescriptionPicker() : null,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getSourceDetailDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _sourceDetailId != null
                          ? const Color(0xFF333333)
                          : _sourceId != null
                              ? Colors.grey[400]
                              : Colors.grey[300],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color:
                        _sourceId != null ? Colors.grey[400] : Colors.grey[300],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHouseTypeField() {
    return Row(
      children: [
        Text(
          '房型',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showHouseTypePicker(),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getRoomTypeDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _houseType != null
                          ? const Color(0xFF333333)
                          : Colors.grey[400],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[400],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoomTypeField() {
    return Row(
      children: [
        Text(
          '户型',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildRoomInput(_roomController, '室'),
              SizedBox(width: 12.w),
              _buildRoomInput(_hallController, '厅'),
              SizedBox(width: 12.w),
              _buildRoomInput(_kitchenController, '厨'),
              SizedBox(width: 12.w),
              _buildRoomInput(_toiletController, '卫'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRoomInput(TextEditingController controller, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 40.w,
          height: 32.h,
          child: Center(
            child: TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF333333),
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
            ),
          ),
        ),
        SizedBox(width: 4.w),
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildAreaField() {
    return Row(
      children: [
        Text(
          '面积',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: TextField(
            controller: _areaController,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.right,
            decoration: InputDecoration(
              hintText: '请输入',
              hintStyle: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[400],
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8.w),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDecorationTypeField() {
    return Row(
      children: [
        Text(
          '线索装修类型',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showDecorationTypePicker(),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getDecorationTypeDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _decorationTypeId != null
                          ? const Color(0xFF333333)
                          : Colors.grey[400],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[400],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDecorationAreaField() {
    return Row(
      children: [
        Text(
          '装修区域',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () => _showDecorationAreaPicker(),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    _getDecorationAreaDisplayName() ?? '请选择',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _decorationAreaId != null
                          ? const Color(0xFF333333)
                          : Colors.grey[400],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey[400],
                    size: 20.w,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailAddressField() {
    return Row(
      children: [
        Text(
          '详细地址',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF333333),
            fontWeight: FontWeight.bold,
          ),
        ),
        Expanded(
          child: TextField(
            controller: _addressController,
            textAlign: TextAlign.right,
            decoration: InputDecoration(
              hintText: '请输入',
              hintStyle: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[400],
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 8.w),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConfirmButton() {
    return Container(
      margin: EdgeInsets.all(16.w),
      child: SizedBox(
        width: double.infinity,
        height: 48.h,
        child: ElevatedButton(
          onPressed: _onConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            '确认',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  // 从选项中获取ID
  String _getIdFromOption(Map<String, dynamic> option) {
    // 对于来源明细数据，优先使用activityId作为唯一标识
    if (option.containsKey('activityId') &&
        option.containsKey('activityName')) {
      return option['activityId'] ?? '';
    }

    return option['sourceId'] ?? // 来源数据
        option['deptId'] ?? // 部门数据
        option['userId'] ?? // 人员数据
        option['value'] ?? // 装修类型等字典数据的value字段
        option['dictId'] ?? // 装修类型等字典数据的dictId字段
        option['id'] ?? // 通用字段
        '';
  }

  // 从选项中获取名称
  String _getNameFromOption(Map<String, dynamic> option) {
    return option['sourceName'] ?? // 来源数据
        option['activityName'] ?? // 来源明细数据
        option['realName'] ?? // 人员数据（优先级提高）
        option['deptName'] ?? // 部门数据
        option['label'] ?? // 装修类型等字典数据的label字段
        option['text'] ?? // 装修类型等字典数据的text字段
        option['dictName'] ?? // 装修类型等字典数据的dictName字段
        option['name'] ?? // 通用名称字段
        '';
  }

  // 获取首次来源显示名称
  String? _getFirstSourceDisplayName() {
    if (_firstSourceId == null || firstSources.isEmpty) return null;
    final source = firstSources.firstWhere(
      (item) => _getIdFromOption(item) == _firstSourceId,
      orElse: () => {},
    );
    return source.isNotEmpty ? _getNameFromOption(source) : null;
  }

  // 获取首次来源明细显示名称
  String? _getFirstSourceDetailDisplayName() {
    if (_firstSourceDetailId == null || firstSourceDetails.isEmpty) return null;
    final detail = firstSourceDetails.firstWhere(
      (item) => _getIdFromOption(item) == _firstSourceDetailId,
      orElse: () => {},
    );
    return detail.isNotEmpty ? _getNameFromOption(detail) : null;
  }

  // 获取来源显示名称
  String? _getSourceDisplayName() {
    if (_sourceId == null || sources.isEmpty) return null;
    final source = sources.firstWhere(
      (item) => _getIdFromOption(item) == _sourceId,
      orElse: () => {},
    );
    return source.isNotEmpty ? _getNameFromOption(source) : null;
  }

  // 获取来源明细显示名称
  String? _getSourceDetailDisplayName() {
    if (_sourceDetailId == null || sourceDetails.isEmpty) return null;
    final detail = sourceDetails.firstWhere(
      (item) => _getIdFromOption(item) == _sourceDetailId,
      orElse: () => {},
    );
    return detail.isNotEmpty ? _getNameFromOption(detail) : null;
  }

  // 获取装修类型显示名称
  String? _getDecorationTypeDisplayName() {
    if (_decorationTypeId == null || decorationTypes.isEmpty) return null;
    final type = decorationTypes.firstWhere(
      (item) => _getIdFromOption(item) == _decorationTypeId,
      orElse: () => {},
    );
    return type.isNotEmpty ? _getNameFromOption(type) : null;
  }

  // 获取房型类型显示名称
  String? _getRoomTypeDisplayName() {
    if (_houseType == null || roomTypes.isEmpty) return null;
    final type = roomTypes.firstWhere(
      (item) => _getIdFromOption(item) == _houseType,
      orElse: () => {},
    );
    return type.isNotEmpty ? _getNameFromOption(type) : null;
  }

  // 获取装修区域显示名称
  String? _getDecorationAreaDisplayName() {
    if (_decorationAreaId == null || decorationAreas.isEmpty) return null;
    final area = decorationAreas.firstWhere(
      (item) => _getIdFromOption(item) == _decorationAreaId,
      orElse: () => {},
    );
    return area.isNotEmpty ? _getNameFromOption(area) : null;
  }

  // 获取项目状态显示名称
  String? _getProjectStatusDisplayName() {
    if (_projectStatusId == null || intentionTypes.isEmpty) return null;
    final intentionType = intentionTypes.firstWhere(
      (item) => _getIdFromOption(item) == _projectStatusId,
      orElse: () => <String, dynamic>{},
    );
    return intentionType.isNotEmpty ? _getNameFromOption(intentionType) : null;
  }

  // 加载首次来源
  Future<void> _loadFirstSources() async {
    setState(() {
      isLoadingFirstSources = true;
    });
    try {
      await _fetchSources();
      firstSources = List.from(sources); // 复制来源数据到首次来源
    } catch (e) {
      print('加载首次来源失败: $e');
    } finally {
      setState(() {
        isLoadingFirstSources = false;
      });
    }
  }

  // 加载来源
  Future<void> _loadSources() async {
    setState(() {
      isLoadingSources = true;
    });
    try {
      await _fetchSources();
    } catch (e) {
      print('加载来源失败: $e');
    } finally {
      setState(() {
        isLoadingSources = false;
      });
    }
  }

  // 加载装修类型
  Future<void> _loadDecorationTypes() async {
    setState(() {
      isLoadingDecorationTypes = true;
    });
    try {
      await _fetchDecorationTypes('crm_decorate_type');
    } catch (e) {
      print('加载装修类型失败: $e');
    } finally {
      setState(() {
        isLoadingDecorationTypes = false;
      });
    }
  }

  // 加载房型类型
  Future<void> _loadRoomTypes() async {
    setState(() {
      isLoadingRoomTypes = true;
    });
    try {
      await _fetchRoomTypes('crm_room_type');
    } catch (e) {
      print('加载房型类型失败: $e');
    } finally {
      setState(() {
        isLoadingRoomTypes = false;
      });
    }
  }

  // 加载装修区域
  Future<void> _loadDecorationAreas() async {
    setState(() {
      isLoadingDecorationAreas = true;
    });
    try {
      await _fetchDecorationAreas();
    } catch (e) {
      print('加载装修区域失败: $e');
    } finally {
      setState(() {
        isLoadingDecorationAreas = false;
      });
    }
  }

  // 加载意向类型
  Future<void> _loadIntentionTypes() async {
    setState(() {
      isLoadingIntentionTypes = true;
    });
    try {
      await _fetchIntentionTypes();
    } catch (e) {
      print('加载意向类型失败: $e');
    } finally {
      setState(() {
        isLoadingIntentionTypes = false;
      });
    }
  }

  // 加载首次来源明细
  Future<void> _loadFirstSourceDetails(String sourceId) async {
    setState(() {
      isLoadingFirstSourceDetails = true;
      firstSourceDetails.clear();
      _firstSourceDetailId = null;
    });
    try {
      await _fetchSourceDetails(sourceId, isFirstSource: true);
    } catch (e) {
      print('加载首次来源明细失败: $e');
      setState(() {
        firstSourceDetails.clear();
        _firstSourceDetailId = null;
      });
    } finally {
      setState(() {
        isLoadingFirstSourceDetails = false;
      });
    }
  }

  // 加载来源明细
  Future<void> _loadSourceDetails(String sourceId) async {
    setState(() {
      isLoadingSourceDetails = true;
      sourceDetails.clear();
      _sourceDetailId = null;
    });
    try {
      await _fetchSourceDetails(sourceId, isFirstSource: false);
    } catch (e) {
      print('加载来源明细失败: $e');
      setState(() {
        sourceDetails.clear();
        _sourceDetailId = null;
      });
    } finally {
      setState(() {
        isLoadingSourceDetails = false;
      });
    }
  }

  // API 调用方法
  // 获取来源/首次来源
  Future<void> _fetchSources() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/source/list',
        queryParameters: null,
      );

      if (response != null && response is List) {
        sources = (response).map((e) => Map<String, dynamic>.from(e)).toList();
        setState(() {});
      }
    } catch (e) {
      print('获取来源失败: $e');
    }
  }

  // 获取来源明细
  Future<void> _fetchSourceDetails(String sourceId,
      {required bool isFirstSource}) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/source/detail',
        queryParameters: {'sourceId': sourceId},
      );

      print('来源明细API响应: $response'); // 调试信息

      if (response != null) {
        List<dynamic>? detailData;

        if (response is List<dynamic>) {
          detailData = response;
        } else if (response is Map<String, dynamic>) {
          detailData = response['data'] as List<dynamic>? ??
              response['list'] as List<dynamic>? ??
              response['details'] as List<dynamic>?;
        }

        if (detailData != null && detailData.isNotEmpty) {
          final validDetails = <Map<String, dynamic>>[];
          final seenIds = <String>{};

          for (final item in detailData) {
            if (item is Map<String, dynamic>) {
              final detail = Map<String, dynamic>.from(item);
              final id = _getIdFromOption(detail);
              final name = _getNameFromOption(detail);

              if (id.isNotEmpty && name.isNotEmpty && !seenIds.contains(id)) {
                seenIds.add(id);
                validDetails.add(detail);
              }
            }
          }

          if (isFirstSource) {
            firstSourceDetails = validDetails;
          } else {
            sourceDetails = validDetails;
          }
          print('来源明细数据处理成功: $validDetails'); // 调试信息
        } else {
          print('来源明细数据为空或格式不正确');
          if (isFirstSource) {
            firstSourceDetails = [];
          } else {
            sourceDetails = [];
          }
        }
        setState(() {});
      } else {
        if (isFirstSource) {
          firstSourceDetails = [];
        } else {
          sourceDetails = [];
        }
        setState(() {});
      }
    } catch (e) {
      print('获取来源明细失败: $e');
      if (isFirstSource) {
        firstSourceDetails = [];
      } else {
        sourceDetails = [];
      }
      setState(() {});
    }
  }

  // 获取装修类型
  Future<void> _fetchDecorationTypes(String path) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/$path',
        queryParameters: null,
      );

      if (response != null) {
        print('装修类型API响应: $response'); // 调试信息
        List<dynamic>? decorationTypeData;

        if (response is Map<String, dynamic>) {
          decorationTypeData =
              response['pbs_decoration_type'] as List<dynamic>? ??
                  response['crm_decorate_type'] as List<dynamic>? ??
                  response['data'] as List<dynamic>? ??
                  response['list'] as List<dynamic>?;
        } else if (response is List<dynamic>) {
          decorationTypeData = response;
        }

        if (decorationTypeData != null && decorationTypeData.isNotEmpty) {
          decorationTypes = decorationTypeData
              .map((e) => Map<String, dynamic>.from(e))
              .toList();
          print('装修类型数据处理成功: $decorationTypes'); // 调试信息
        } else {
          print('装修类型数据为空或格式不正确');
          decorationTypes = [];
        }
        setState(() {});
      }
    } catch (e) {
      print('获取装修类型失败: $e');
      decorationTypes = [];
      setState(() {});
    }
  }

  // 获取房型类型
  Future<void> _fetchRoomTypes(String path) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/$path',
        queryParameters: null,
      );

      if (response != null) {
        print('房型类型API响应: $response'); // 调试信息
        List<dynamic>? roomTypeData;

        if (response is Map<String, dynamic>) {
          roomTypeData =
              response['crm_room_type'] as List<dynamic>? ??
                  response['data'] as List<dynamic>? ??
                  response['list'] as List<dynamic>?;
        } else if (response is List<dynamic>) {
          roomTypeData = response;
        }

        if (roomTypeData != null && roomTypeData.isNotEmpty) {
          roomTypes = roomTypeData
              .map((e) => Map<String, dynamic>.from(e))
              .toList();
          print('房型类型数据处理成功: $roomTypes'); // 调试信息
        } else {
          print('房型类型数据为空或格式不正确');
          roomTypes = [];
        }
        setState(() {});
      }
    } catch (e) {
      print('获取房型类型失败: $e');
      roomTypes = [];
      setState(() {});
    }
  }

  // 获取装修区域
  Future<void> _fetchDecorationAreas() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/decoration_area',
        queryParameters: null,
      );

      if (response != null) {
        print('装修区域API响应: $response'); // 调试信息
        List<dynamic>? decorationAreaData;

        if (response is Map<String, dynamic>) {
          decorationAreaData =
              response['decoration_area'] as List<dynamic>? ??
                  response['data'] as List<dynamic>? ??
                  response['list'] as List<dynamic>?;
        } else if (response is List<dynamic>) {
          decorationAreaData = response;
        }

        if (decorationAreaData != null && decorationAreaData.isNotEmpty) {
          decorationAreas = decorationAreaData
              .map((e) => Map<String, dynamic>.from(e))
              .toList();
          print('装修区域数据处理成功: $decorationAreas'); // 调试信息
        } else {
          print('装修区域数据为空或格式不正确');
          decorationAreas = [];
        }
        setState(() {});
      }
    } catch (e) {
      print('获取装修区域失败: $e');
      decorationAreas = [];
      setState(() {});
    }
  }

  // 处理首次来源选择变化
  void _handleFirstSourceChange(String? value) {
    if (value != null && value != _firstSourceId) {
      setState(() {
        _firstSourceId = value;
        _firstSourceDetailId = null;
        firstSourceDetails.clear();
      });
      _loadFirstSourceDetails(value);
    } else if (value == null) {
      setState(() {
        _firstSourceId = null;
        _firstSourceDetailId = null;
        firstSourceDetails.clear();
      });
    }
  }

  // 处理来源选择变化
  void _handleSourceChange(String? value) {
    if (value != null && value != _sourceId) {
      setState(() {
        _sourceId = value;
        _sourceDetailId = null;
        sourceDetails.clear();
      });
      _loadSourceDetails(value);
    } else if (value == null) {
      setState(() {
        _sourceId = null;
        _sourceDetailId = null;
        sourceDetails.clear();
      });
    }
  }

  // API 数据选择器方法
  void _showFirstSourcePicker() {
    if (firstSources.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择首次来源',
        options: firstSources,
        currentValue: _firstSourceId,
        onSelected: (value) {
          _handleFirstSourceChange(value);
        },
      ),
    );
  }

  void _showFirstSourceDescriptionPicker() {
    if (firstSourceDetails.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择首次来源明细',
        options: firstSourceDetails,
        currentValue: _firstSourceDetailId,
        onSelected: (value) {
          setState(() {
            _firstSourceDetailId = value;
          });
        },
      ),
    );
  }

  void _showSourcePicker() {
    if (sources.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择来源',
        options: sources,
        currentValue: _sourceId,
        onSelected: (value) {
          _handleSourceChange(value);
        },
      ),
    );
  }

  void _showSourceDescriptionPicker() {
    if (sourceDetails.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择来源明细',
        options: sourceDetails,
        currentValue: _sourceDetailId,
        onSelected: (value) {
          setState(() {
            _sourceDetailId = value;
          });
        },
      ),
    );
  }

  void _showDecorationTypePicker() {
    if (decorationTypes.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择装修类型',
        options: decorationTypes,
        currentValue: _decorationTypeId,
        onSelected: (value) {
          setState(() {
            _decorationTypeId = value;
          });
        },
      ),
    );
  }

  void _showDecorationAreaPicker() {
    if (decorationAreas.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择装修区域',
        options: decorationAreas,
        currentValue: _decorationAreaId,
        onSelected: (value) {
          setState(() {
            _decorationAreaId = value;
          });
        },
      ),
    );
  }

  // 选择器方法
  void _showProjectStatusPicker() {
    if (intentionTypes.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择项目状态',
        options: intentionTypes,
        currentValue: _projectStatusId,
        onSelected: (value) {
          setState(() {
            _projectStatusId = value;
          });
        },
      ),
    );
  }

  // 获取意向类型数据
  Future<void> _fetchIntentionTypes() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/customer_info_now_status',
        queryParameters: null,
      );

      if (response != null) {
        List<dynamic>? intentionTypeData;

        if (response is Map<String, dynamic>) {
          intentionTypeData =
              response['customer_info_now_status'] as List<dynamic>?;
        } else if (response is List<dynamic>) {
          intentionTypeData = response;
        }

        if (intentionTypeData != null && intentionTypeData.isNotEmpty) {
          intentionTypes = intentionTypeData
              .map((e) => Map<String, dynamic>.from(e))
              .toList();
          print('意向类型数据处理成功: $intentionTypes'); // 调试信息

          // 默认选择第二个元素（如果存在）
          if (intentionTypes.length >= 2 && _projectStatusId == null) {
            _projectStatusId = _getIdFromOption(intentionTypes[1]);
          }
        } else {
          print('意向类型数据为空或格式不正确');
          intentionTypes = [];
        }
        setState(() {});
      }
    } catch (e) {
      print('获取意向类型失败: $e');
      intentionTypes = [];
      setState(() {});
    }
  }

  // 构建API数据选择器
  Widget _buildApiPicker({
    required String title,
    required List<Map<String, dynamic>> options,
    required String? currentValue,
    required Function(String?) onSelected,
  }) {
    return Container(
      height: 400.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        children: [
          // 顶部拖拽指示器
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.only(top: 8.h),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // 标题栏和按钮
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              children: [
                // 重置按钮（只显示图标）
                GestureDetector(
                  onTap: () {
                    onSelected(null);
                    Navigator.pop(context);
                  },
                  child: Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Icon(
                      Icons.refresh,
                      size: 20.w,
                      color: Colors.grey[600],
                    ),
                  ),
                ),

                // 标题居中
                Expanded(
                  child: Center(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF333333),
                      ),
                    ),
                  ),
                ),

                // 关闭按钮
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Icon(
                      Icons.close,
                      size: 20.w,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 分割线
          Container(
            height: 1.h,
            color: Colors.grey[200],
          ),

          // 选项列表
          Expanded(
            child: Container(
              color: Colors.white,
              child: ListView.builder(
                itemCount: options.length,
                itemBuilder: (context, index) {
                  final option = options[index];
                  final id = _getIdFromOption(option);
                  final name = _getNameFromOption(option);
                  final isSelected = id == currentValue;

                  if (id.isEmpty || name.isEmpty) {
                    return const SizedBox.shrink();
                  }

                  return GestureDetector(
                    onTap: () {
                      onSelected(id);
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 12.h, horizontal: 16.w),
                      decoration: BoxDecoration(
                        color:
                            isSelected ? const Color(0xFFFFF4E0) : Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey[100]!,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Text(
                        name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: isSelected
                              ? const Color(0xFFFFAF2A)
                              : const Color(0xFF333333),
                          fontWeight:
                              isSelected ? FontWeight.w500 : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showHouseTypePicker() {
    if (roomTypes.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => _buildApiPicker(
        title: '选择房型',
        options: roomTypes,
        currentValue: _houseType,
        onSelected: (value) {
          setState(() {
            _houseType = value;
          });
        },
      ),
    );
  }



  void _onConfirm() {
    // 验证必填字段
    if (_phoneController.text.trim().isEmpty) {
      _showErrorDialog('请输入手机号');
      return;
    }

    if (_nameController.text.trim().isEmpty) {
      _showErrorDialog('请输入姓名');
      return;
    }

    if (_projectStatusId == null) {
      _showErrorDialog('请选择项目状态');
      return;
    }

    if (_firstSourceId == null) {
      _showErrorDialog('请选择首次来源');
      return;
    }

    if (_firstSourceDetailId == null) {
      _showErrorDialog('请选择首次来源明细');
      return;
    }

    if (_sourceId == null) {
      _showErrorDialog('请选择来源');
      return;
    }

    if (_sourceDetailId == null) {
      _showErrorDialog('请选择来源明细');
      return;
    }

    // 显示成功提示
    _showSuccessDialog();
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提示'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('成功'),
        content: const Text('用户信息已保存'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
              Navigator.pop(context); // 返回上一页
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
